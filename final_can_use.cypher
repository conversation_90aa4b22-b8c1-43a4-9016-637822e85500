WITH "_GET" AS pSrc, "call_user_func" AS pSnk
CALL apoc.cypher.runTimeboxed("
WITH $srcName AS srcName, $snkName AS snkName

// 1) sink 与所有参数候选（排除 RETURN / METHOD_RETURN / LITERAL）
MATCH (snk:CALL)
WHERE snk.NAME = snkName OR snk.METHOD_FULL_NAME = snkName
MATCH (snk)-[:ARGUMENT]-(cand)
WITH snk, srcName, [a IN collect(DISTINCT cand) WHERE NOT 'LITERAL' IN labels(a)
                                                AND NOT 'RETURN' IN labels(a)
                                                AND NOT 'METHOD_RETURN' IN labels(a)] AS sinkArgs

// 2) 源集合（保持不变）
MATCH (base)
WHERE (base:IDENTIFIER AND base.NAME = srcName)
   OR (base:LOCAL AND base.NAME = srcName)
   OR (base:CALL AND base.METHOD_FULL_NAME = '<operator>.indexAccess'
       AND exists( (base)-[:ARGUMENT]->(:IDENTIFIER {NAME: srcName}) ))
WITH snk, sinkArgs, collect(base) AS sources

// 3) 每个 sinkArg 独立搜索（DFS + endNodes: [arg]）
UNWIND sinkArgs AS arg
UNWIND sources AS s
CALL apoc.path.expandConfig(s, {
  endNodes: [arg],
  relationshipFilter: 'REF||ARGUMENT|PARAMETER_LINK|REACHING_DEF|RECEIVER|CALL|CONTAINS',
  minLevel: 1,
  maxLevel: 800,
  bfs: false,                       // DFS
  uniqueness: 'NODE_GLOBAL',
  labelFilter: '+IDENTIFIER|+LOCAL|+CALL|+METHOD|+METHOD_PARAMETER_IN|+METHOD_PARAMETER_OUT|-METHOD_RETURN|-LITERAL'
}) YIELD path

// 4) 必须终到该 arg，且路径中至少含一次 REACHING_DEF
WITH snk, arg, path
WHERE last(nodes(path)) = arg
  AND any(r IN relationships(path) WHERE type(r)='REACHING_DEF')

// 5) 对“每个 arg”取最长路径（关键：以 arg 为分组键）
ORDER BY length(path) DESC
WITH snk, arg, head(collect(path)) AS bestPerArg

// 6) 输出：每个 arg 一行，nodes 追加 sink CALL 节点
WITH [n IN nodes(bestPerArg) | { id:id(n), labels:labels(n), properties:properties(n) }] AS pathNodes, snk
RETURN pathNodes + [{ id:id(snk), labels:labels(snk), properties:properties(snk) }] AS nodes
", {srcName: pSrc, snkName: pSnk}, 12000) YIELD value
RETURN value.nodes AS nodes;