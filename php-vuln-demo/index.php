<?php
require_once 'functions.php';
require_once 'Handler.php';
require_once 'Processor.php';

/**
 * 主路由处理函数 - 看似安全的入口点
 */
function handleRequest() {
    $operation = $_GET['op'] ?? 'info';
    $target = $_GET['target'] ?? '';
    $params = $_GET['params'] ?? '';
    
    #echo  "[ROUTER] Processing operation: $operation<br>\n";
    
    // 第一层：路由到不同的处理器
    switch ($operation) {
        case 'info':
            return processInformation($target, $params);
        case 'manage':
            return processManagement($target, $params);
        case 'analyze':
            return processAnalysis($target, $params);
        case 'transform':
            return processTransformation($target, $params);
        default:
            return "Unknown operation: $operation";
    }
}

/**
 * 信息处理入口 - 看似安全的函数
 */
function processInformation($target, $params) {
    #echo  "[INFO] Target: $target, Params: $params<br>\n";
    
    if (empty($target)) {
        return "Available targets: system, file, network";
    }
    
    // 调用工具函数进行进一步处理
    return delegateToUtility('info_' . $target, $params);
}

/**
 * 管理处理入口 - 包含复杂的验证逻辑
 */
function processManagement($target, $params) {
    #echo  "[MANAGE] Target: $target, Params: $params<br>\n";
    
    // 多层验证，但有绕过可能
    if (!isValidTarget($target)) {
        return "Invalid target specified";
    }
    
    // 创建Handler实例并委托
    $handler = new DataHandler();
    return $handler->processRequest($target, $params);
}

/**
 * 分析处理入口 - 委托给专门的分析器
 */
function processAnalysis($target, $params) {
    #echo  "[ANALYZE] Target: $target, Params: $params<br>\n";
    
    $analyzer = new SystemAnalyzer();
    return $analyzer->performAnalysis($target, $params);
}

/**
 * 转换处理入口 - 最终通向漏洞的路径
 */
function processTransformation($target, $params) {
    #echo  "[TRANSFORM] Target: $target, Params: $params<br>\n";
    
    // 这里看似有验证，但可以被绕过
    if (strpos($target, 'safe_') !== 0 && strpos($target, 'transform_') !== 0) {
        // 用户可能认为这里安全了，但实际上可以构造绕过
        #echo  "[SECURITY] Attempting unsafe operation, redirecting...<br>\n";
        $target = 'safe_' . $target; // 简单的前缀添加，可被利用
    }
    
    // 委托给核心处理器 - 这里是通向真正漏洞的路径
    $processor = new CoreProcessor();
    return $processor->execute($target, $params);
}

// 主执行逻辑
if (php_sapi_name() === 'cli' || isset($_GET['op'])) {
    echo  handleRequest() . "<br>\n";
} else {
    #echo  "PHP Advanced Vulnerability Demo<br>\n\r";
    #echo  "Usage Examples:<br>\n\r";
    #echo  "?op=info&target=system<br>\n\r";
    #echo  "?op=manage&target=data&params=test<br>\n\r";
    #echo  "?op=analyze&target=performance<br>\n\r";
    #echo  "?op=transform&target=safe_demo&params=value<br>\n\r";
}
?>
