<?php

/**
 * 数据处理器类 - 看似复杂但实际安全的实现
 */
class DataHandler {
    private $processingChain = [];
    
    public function __construct() {
        $this->processingChain = [
            'data' => 'handleDataProcessing',
            'file' => 'handleFileProcessing',
            'cache' => 'handleCacheProcessing',
            'log' => 'handleLogProcessing'
        ];
    }
    
    /**
     * 主请求处理方法 - 第四层调用
     */
    public function processRequest($target, $params) {
        #echo  "[HANDLER] DataHandler processing target: $target<br>\n";
        
        // 检查处理链中是否有对应的处理器
        if (isset($this->processingChain[$target])) {
            $method = $this->processingChain[$target];
            #echo  "[HANDLER] Using method: $method<br>\n";
            
            // 这里是安全的内部方法调用
            return $this->$method($params);
        }
        
        // 如果不在预定义链中，委托给通用处理器
        return $this->handleGenericProcessing($target, $params);
    }
    
    /**
     * 数据处理 - 安全实现
     */
    private function handleDataProcessing($params) {
        #echo  "[DATA] Processing data: $params<br>\n";
        return "Data processed: " . htmlspecialchars($params);
    }
    
    /**
     * 文件处理 - 带安全检查的实现
     */
    private function handleFileProcessing($params) {
        #echo  "[FILE] Processing file request: $params<br>\n";
        
        if (empty($params) || strpos($params, '/') !== false) {
            return "Invalid file parameter";
        }
        
        return "File processing result for: $params (safe)";
    }
    
    /**
     * 缓存处理 - 安全的缓存操作
     */
    private function handleCacheProcessing($params) {
        #echo  "[CACHE] Cache operation: $params<br>\n";
        return "Cache operation completed: $params";
    }
    
    /**
     * 通用处理器 - 看似危险但实际安全
     */
    private function handleGenericProcessing($target, $params) {
        #echo  "[GENERIC] Generic processing for: $target<br>\n";
        
        // 这里看似可能有问题，但实际上是安全的
        $safeTargets = ['info', 'status', 'health', 'version'];
        
        if (in_array($target, $safeTargets)) {
            return "Generic result for $target: " . htmlspecialchars($params);
        }
        
        return "Target not supported in generic handler: $target";
    }
}

/**
 * 系统分析器类 - 复杂的分析逻辑但不包含漏洞
 */
class SystemAnalyzer {
    private $analysisModules;
    
    public function __construct() {
        $this->analysisModules = [
            'performance' => new PerformanceAnalyzer(),
            'security' => new SecurityAnalyzer(),
            'resource' => new ResourceAnalyzer()
        ];
    }
    
    /**
     * 执行分析 - 第四层调用，委托给专门的分析器
     */
    public function performAnalysis($target, $params) {
        #echo  "[ANALYZER] System analysis for: $target<br>\n";
        
        if (isset($this->analysisModules[$target])) {
            $analyzer = $this->analysisModules[$target];
            return $analyzer->analyze($params);
        }
        
        return $this->performBasicAnalysis($target, $params);
    }
    
    /**
     * 基本分析 - 安全的回退处理
     */
    private function performBasicAnalysis($target, $params) {
        #echo  "[BASIC] Basic analysis for: $target<br>\n";
        return "Basic analysis completed for $target with params: " . htmlspecialchars($params);
    }
    
    /**
     * 获取分析报告 - 安全的信息返回
     */
    public function getReport($type) {
        #echo  "[REPORT] Generating report: $type<br>\n";
        return "Analysis report for $type generated safely";
    }
    
    /**
     * 系统状态检查 - 安全的状态查询
     */
    public function checkStatus($component) {
        #echo  "[STATUS] Checking status: $component<br>\n";
        return "Component $component status: OK";
    }
}

/**
 * 性能分析器 - 专门的分析实现
 */
class PerformanceAnalyzer {
    public function analyze($params) {
        #echo  "[PERF] Performance analysis with: $params<br>\n";
        return "Performance metrics: CPU OK, Memory OK, Params: " . htmlspecialchars($params);
    }
}

/**
 * 安全分析器 - 专门的安全检查
 */
class SecurityAnalyzer {
    public function analyze($params) {
        #echo  "[SEC] Security analysis with: $params<br>\n";
        return "Security scan completed, no threats detected. Params: " . htmlspecialchars($params);
    }
}

/**
 * 资源分析器 - 资源使用情况分析
 */
class ResourceAnalyzer {
    public function analyze($params) {
        #echo  "[RES] Resource analysis with: $params<br>\n";
        return "Resource utilization normal. Params: " . htmlspecialchars($params);
    }
}

?>
