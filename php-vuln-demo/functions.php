<?php

/**
 * 工具委托函数 - 第二层调用
 * 看似进行安全检查，但实际上可以被绕过
 */
function delegateToUtility($functionName, $params) {
    #echo  "[UTIL] Delegating to: $functionName<br>\n";
    
    // 看似安全的函数名检查
    $allowedPrefixes = ['info_', 'util_', 'helper_', 'safe_'];
    $hasValidPrefix = false;
    
    foreach ($allowedPrefixes as $prefix) {
        if (strpos($functionName, $prefix) === 0) {
            $hasValidPrefix = true;
            break;
        }
    }
    
    if (!$hasValidPrefix) {
        return "Function prefix not allowed: $functionName";
    }
    
    // 进一步委托给专门的工具处理器
    return invokeUtilityFunction($functionName, $params);
}

/**
 * 目标验证函数 - 看似严格但有逻辑漏洞
 */
function isValidTarget($target) {
    #echo  "[VALID] Checking target: $target<br>\n";
    
    // 黑名单检查，但不够全面
    $blacklist = ['system', 'exec', 'shell', 'eval'];
    
    foreach ($blacklist as $blocked) {
        if (strpos($target, $blocked) !== false) {
            #echo  "[VALID] Blocked keyword found: $blocked<br>\n";
            return false;
        }
    }
    
    // 长度和字符检查
    if (strlen($target) > 50 || !preg_match('/^[a-zA-Z0-9_]+$/', $target)) {
        #echo  "[VALID] Invalid format or too long<br>\n";
        return false;
    }
    
    return true;
}

/**
 * 工具函数调用器 - 第三层调用，增加复杂性
 */
function invokeUtilityFunction($funcName, $params) {
    #echo  "[INVOKE] Invoking utility: $funcName with params: $params<br>\n";
    
    // 模拟的函数映射表
    $functionMap = [
        'info_system' => 'getSystemInformation',
        'info_file' => 'getFileInformation',
        'info_network' => 'getNetworkInformation',
        'util_format' => 'formatData',
        'helper_validate' => 'validateInput',
        'safe_process' => 'processSafely'
    ];
    
    // 如果在映射表中，直接调用映射的函数
    if (isset($functionMap[$funcName])) {
        $realFunc = $functionMap[$funcName];
        #echo  "[INVOKE] Mapped to: $realFunc<br>\n";
        
        if (function_exists($realFunc)) {
            return $realFunc($params);
        } else {
            return "Mapped function does not exist: $realFunc";
        }
    }
    
    // 如果不在映射表中，返回错误（这里其实是安全的）
    return "Utility function not found in mapping: $funcName";
}

/**
 * 系统信息获取 - 安全的实现函数
 */
function getSystemInformation($type) {
    switch ($type) {
        case 'php':
            return "PHP Version: " . PHP_VERSION;
        case 'os':
            return "OS: " . PHP_OS;
        case 'memory':
            return "Memory: " . memory_get_usage() . " bytes";
        default:
            return "System info type: $type (safe)";
    }
}

/**
 * 文件信息获取 - 带有基本安全检查
 */
function getFileInformation($filename) {
    if (empty($filename)) {
        return "No filename provided";
    }
    
    // 基本路径遍历防护
    if (strpos($filename, '..') !== false) {
        return "Path traversal detected";
    }
    
    if (file_exists($filename) && is_readable($filename)) {
        return "File exists: $filename (" . filesize($filename) . " bytes)";
    }
    
    return "File not accessible: $filename";
}

/**
 * 网络信息获取 - 示例安全函数
 */
function getNetworkInformation($target) {
    // 简单的网络信息，避免实际网络调用
    return "Network target: $target (simulated check - safe)";
}

/**
 * 数据格式化 - 安全的工具函数
 */
function formatData($data) {
    return "Formatted: " . htmlspecialchars($data);
}

?>
