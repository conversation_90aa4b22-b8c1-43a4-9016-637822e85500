111669149696,<PERSON><PERSON><PERSON>,<PERSON>ler.php:<global>,TYPE_DECL,VIRTUAL PUBLIC STATIC function <global>(),,,Handler.php,Handler.php:<global>,<empty>,,false,6,,<global>,,,1,<unresolvedSignature>(0)
111669149697,<PERSON><PERSON><PERSON>,DataHandler,TYPE_DECL,PUBLIC function __construct(this),,,Handler.php,DataHandler->__construct,<empty>,,false,9,,__construct,,,2,<unresolvedSignature>(0)
111669149698,<PERSON><PERSON><PERSON>,<PERSON>Hand<PERSON>,TYPE_DECL,"PUBLIC function processRequest(this,$target,$params)",,,Handler.php,DataHandler->processRequest,<empty>,,false,21,,processRequest,,,3,<unresolvedSignature>(2)
111669149699,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>_DECL,"PRIVATE function handleDataProcessing(this,$params)",,,<PERSON>ler.php,DataHandler->handleDataProcessing,<empty>,,false,40,,handleDataProcessing,,,4,<unresolvedSignature>(1)
111669149700,METHOD,DataHandler,TYPE_DECL,"PRIVATE function handleFileProcessing(this,$params)",,,Handler.php,DataHandler->handleFileProcessing,<empty>,,false,48,,handleFileProcessing,,,5,<unresolvedSignature>(1)
111669149701,METHOD,DataHandler,TYPE_DECL,"PRIVATE function handleCacheProcessing(this,$params)",,,Handler.php,DataHandler->handleCacheProcessing,<empty>,,false,61,,handleCacheProcessing,,,6,<unresolvedSignature>(1)
111669149702,METHOD,DataHandler,TYPE_DECL,"PRIVATE function handleGenericProcessing(this,$target,$params)",,,Handler.php,DataHandler->handleGenericProcessing,<empty>,,false,69,,handleGenericProcessing,,,7,<unresolvedSignature>(2)
111669149703,METHOD,SystemAnalyzer,TYPE_DECL,PUBLIC function __construct(this),,,Handler.php,SystemAnalyzer->__construct,<empty>,,false,89,,__construct,,,2,<unresolvedSignature>(0)
111669149704,METHOD,SystemAnalyzer,TYPE_DECL,"PUBLIC function performAnalysis(this,$target,$params)",,,Handler.php,SystemAnalyzer->performAnalysis,<empty>,,false,100,,performAnalysis,,,3,<unresolvedSignature>(2)
111669149705,METHOD,SystemAnalyzer,TYPE_DECL,"PRIVATE function performBasicAnalysis(this,$target,$params)",,,Handler.php,SystemAnalyzer->performBasicAnalysis,<empty>,,false,114,,performBasicAnalysis,,,4,<unresolvedSignature>(2)
111669149706,METHOD,SystemAnalyzer,TYPE_DECL,"PUBLIC function getReport(this,$type)",,,Handler.php,SystemAnalyzer->getReport,<empty>,,false,122,,getReport,,,5,<unresolvedSignature>(1)
111669149707,METHOD,SystemAnalyzer,TYPE_DECL,"PUBLIC function checkStatus(this,$component)",,,Handler.php,SystemAnalyzer->checkStatus,<empty>,,false,130,,checkStatus,,,6,<unresolvedSignature>(1)
111669149708,METHOD,PerformanceAnalyzer,TYPE_DECL,PerformanceAnalyzer->__construct,,,Handler.php,PerformanceAnalyzer->__construct,<empty>,,false,139,,__construct,,,1,<unresolvedSignature>(0)
111669149709,METHOD,PerformanceAnalyzer,TYPE_DECL,"PUBLIC function analyze(this,$params)",,,Handler.php,PerformanceAnalyzer->analyze,<empty>,,false,140,,analyze,,,2,<unresolvedSignature>(1)
111669149710,METHOD,SecurityAnalyzer,TYPE_DECL,SecurityAnalyzer->__construct,,,Handler.php,SecurityAnalyzer->__construct,<empty>,,false,149,,__construct,,,1,<unresolvedSignature>(0)
111669149711,METHOD,SecurityAnalyzer,TYPE_DECL,"PUBLIC function analyze(this,$params)",,,Handler.php,SecurityAnalyzer->analyze,<empty>,,false,150,,analyze,,,2,<unresolvedSignature>(1)
111669149712,METHOD,ResourceAnalyzer,TYPE_DECL,ResourceAnalyzer->__construct,,,Handler.php,ResourceAnalyzer->__construct,<empty>,,false,159,,__construct,,,1,<unresolvedSignature>(0)
111669149713,METHOD,ResourceAnalyzer,TYPE_DECL,"PUBLIC function analyze(this,$params)",,,Handler.php,ResourceAnalyzer->analyze,<empty>,,false,160,,analyze,,,2,<unresolvedSignature>(1)
111669149714,METHOD,Processor.php:<global>,TYPE_DECL,VIRTUAL PUBLIC STATIC function <global>(),,,Processor.php,Processor.php:<global>,<empty>,,false,7,,<global>,,,1,<unresolvedSignature>(0)
111669149715,METHOD,CoreProcessor,TYPE_DECL,PUBLIC function __construct(this),,,Processor.php,CoreProcessor->__construct,<empty>,,false,11,,__construct,,,3,<unresolvedSignature>(0)
111669149716,METHOD,CoreProcessor,TYPE_DECL,"PUBLIC function execute(this,$target,$params)",,,Processor.php,CoreProcessor->execute,<empty>,,false,27,,execute,,,4,<unresolvedSignature>(2)
111669149717,METHOD,CoreProcessor,TYPE_DECL,"PRIVATE function isSecureTarget(this,$target)",,,Processor.php,CoreProcessor->isSecureTarget,<empty>,,false,52,,isSecureTarget,,,5,<unresolvedSignature>(1)
111669149718,METHOD,CoreProcessor,TYPE_DECL,"PRIVATE function processSecureTarget(this,$target,$params)",,,Processor.php,CoreProcessor->processSecureTarget,<empty>,,false,93,,processSecureTarget,,,6,<unresolvedSignature>(2)
111669149719,METHOD,CoreProcessor,TYPE_DECL,"PRIVATE function performDefaultProcessing(this,$funcName,$params)",,,Processor.php,CoreProcessor->performDefaultProcessing,<empty>,,false,127,,performDefaultProcessing,,,7,<unresolvedSignature>(2)
111669149720,METHOD,CoreProcessor,TYPE_DECL,"PRIVATE function performSafeDemo(this,$params)",,,Processor.php,CoreProcessor->performSafeDemo,<empty>,,false,135,,performSafeDemo,,,8,<unresolvedSignature>(1)
111669149721,METHOD,CoreProcessor,TYPE_DECL,"PRIVATE function performSafeInfo(this,$params)",,,Processor.php,CoreProcessor->performSafeInfo,<empty>,,false,142,,performSafeInfo,,,9,<unresolvedSignature>(1)
111669149722,METHOD,CoreProcessor,TYPE_DECL,"PRIVATE function performSafeFormat(this,$params)",,,Processor.php,CoreProcessor->performSafeFormat,<empty>,,false,149,,performSafeFormat,,,10,<unresolvedSignature>(1)
111669149723,METHOD,CoreProcessor,TYPE_DECL,"PRIVATE function transformData(this,$params)",,,Processor.php,CoreProcessor->transformData,<empty>,,false,156,,transformData,,,11,<unresolvedSignature>(1)
111669149724,METHOD,functions.php:<global>,TYPE_DECL,VIRTUAL PUBLIC STATIC function <global>(),,,functions.php,functions.php:<global>,<empty>,,false,7,,<global>,,,1,<unresolvedSignature>(0)
111669149725,METHOD,functions.php:<global>,METHOD,"function delegateToUtility($functionName,$params)",,,functions.php,delegateToUtility,<empty>,,false,7,,delegateToUtility,,,1,<unresolvedSignature>(2)
111669149726,METHOD,functions.php:<global>,METHOD,function isValidTarget($target),,,functions.php,isValidTarget,<empty>,,false,32,,isValidTarget,,,2,<unresolvedSignature>(1)
111669149727,METHOD,functions.php:<global>,METHOD,"function invokeUtilityFunction($funcName,$params)",,,functions.php,invokeUtilityFunction,<empty>,,false,57,,invokeUtilityFunction,,,3,<unresolvedSignature>(2)
111669149728,METHOD,functions.php:<global>,METHOD,function getSystemInformation($type),,,functions.php,getSystemInformation,<empty>,,false,89,,getSystemInformation,,,4,<unresolvedSignature>(1)
111669149729,METHOD,functions.php:<global>,METHOD,function getFileInformation($filename),,,functions.php,getFileInformation,<empty>,,false,105,,getFileInformation,,,5,<unresolvedSignature>(1)
111669149730,METHOD,functions.php:<global>,METHOD,function getNetworkInformation($target),,,functions.php,getNetworkInformation,<empty>,,false,125,,getNetworkInformation,,,6,<unresolvedSignature>(1)
111669149731,METHOD,functions.php:<global>,METHOD,function formatData($data),,,functions.php,formatData,<empty>,,false,133,,formatData,,,7,<unresolvedSignature>(1)
111669149732,METHOD,index.php:<global>,TYPE_DECL,VIRTUAL PUBLIC STATIC function <global>(),,,index.php,index.php:<global>,<empty>,,false,2,,<global>,,,1,<unresolvedSignature>(0)
111669149733,METHOD,index.php:<global>,METHOD,function handleRequest(),,,index.php,handleRequest,<empty>,,false,9,,handleRequest,,,5,<unresolvedSignature>(0)
111669149734,METHOD,index.php:<global>,METHOD,"function processInformation($target,$params)",,,index.php,processInformation,<empty>,,false,34,,processInformation,,,6,<unresolvedSignature>(2)
111669149735,METHOD,index.php:<global>,METHOD,"function processManagement($target,$params)",,,index.php,processManagement,<empty>,,false,48,,processManagement,,,7,<unresolvedSignature>(2)
111669149736,METHOD,index.php:<global>,METHOD,"function processAnalysis($target,$params)",,,index.php,processAnalysis,<empty>,,false,64,,processAnalysis,,,8,<unresolvedSignature>(2)
111669149737,METHOD,index.php:<global>,METHOD,"function processTransformation($target,$params)",,,index.php,processTransformation,<empty>,,false,74,,processTransformation,,,9,<unresolvedSignature>(2)
111669149738,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.assignment,<empty>,,true,,,<operator>.assignment,,,0,
111669149739,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.fieldAccess,<empty>,,true,,,<operator>.fieldAccess,,,0,
111669149740,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,array,<empty>,,true,,,array,,,0,array()
111669149741,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.indexAccess,<empty>,,true,,,<operator>.indexAccess,,,0,
111669149742,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,isset,<empty>,,true,,,isset,,,0,
111669149743,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->$method,<empty>,,true,,,$method,,,0,<unresolvedSignature>(1)
111669149744,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->handleGenericProcessing,<empty>,,true,,,handleGenericProcessing,,,0,<unresolvedSignature>(2)
111669149745,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.concat,<empty>,,true,,,<operator>.concat,,,0,
111669149746,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,htmlspecialchars,<empty>,,true,,,htmlspecialchars,,,0,<unresolvedSignature>(1)
111669149747,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.logicalOr,<empty>,,true,,,<operator>.logicalOr,,,0,
111669149748,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,empty,<empty>,,true,,,empty,,,0,
111669149749,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.notIdentical,<empty>,,true,,,<operator>.notIdentical,,,0,
111669149750,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,strpos,<empty>,,true,,,strpos,,,0,<unresolvedSignature>(2)
111669149751,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,encaps,<empty>,,true,,,encaps,,,0,
111669149752,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,in_array,<empty>,,true,,,in_array,,,0,<unresolvedSignature>(2)
111669149753,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.alloc,<empty>,,true,,,<operator>.alloc,,,0,
111669149754,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$analyzer->analyze,<empty>,,true,,,analyze,,,0,<unresolvedSignature>(1)
111669149755,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->performBasicAnalysis,<empty>,,true,,,performBasicAnalysis,,,0,<unresolvedSignature>(2)
111669149756,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->isSecureTarget,<empty>,,true,,,isSecureTarget,,,0,<unresolvedSignature>(1)
111669149757,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->processSecureTarget,<empty>,,true,,,processSecureTarget,,,0,<unresolvedSignature>(2)
111669149758,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,Iterator.current,<empty>,,true,,,current,,,0,<unresolvedSignature>(0)
111669149759,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.logicalNot,<empty>,,true,,,<operator>.logicalNot,,,0,
111669149760,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,is_null,<empty>,,true,,,is_null,,,0,
111669149761,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,Iterator.next,<empty>,,true,,,next,,,0,void()
111669149762,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.greaterThan,<empty>,,true,,,<operator>.greaterThan,,,0,
111669149763,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,strlen,<empty>,,true,,,strlen,,,0,<unresolvedSignature>(1)
111669149764,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.identical,<empty>,,true,,,<operator>.identical,,,0,
111669149765,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,substr,<empty>,,true,,,substr,,,0,<unresolvedSignature>(2)
111669149766,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,function_exists,<empty>,,true,,,function_exists,,,0,<unresolvedSignature>(1)
111669149767,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,call_user_func,<empty>,,true,,,call_user_func,,,0,<unresolvedSignature>(2)
111669149768,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$this->performDefaultProcessing,<empty>,,true,,,performDefaultProcessing,,,0,<unresolvedSignature>(2)
111669149769,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,strtoupper,<empty>,,true,,,strtoupper,,,0,<unresolvedSignature>(1)
111669149770,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,base64_encode,<empty>,,true,,,base64_encode,,,0,<unresolvedSignature>(1)
111669149771,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,preg_match,<empty>,,true,,,preg_match,,,0,<unresolvedSignature>(2)
111669149772,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,$realFunc,<empty>,,true,,,$realFunc,,,0,<unresolvedSignature>(1)
111669149773,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,memory_get_usage,<empty>,,true,,,memory_get_usage,,,0,<unresolvedSignature>(0)
111669149774,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.logicalAnd,<empty>,,true,,,<operator>.logicalAnd,,,0,
111669149775,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,file_exists,<empty>,,true,,,file_exists,,,0,<unresolvedSignature>(1)
111669149776,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,is_readable,<empty>,,true,,,is_readable,,,0,<unresolvedSignature>(1)
111669149777,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,filesize,<empty>,,true,,,filesize,,,0,<unresolvedSignature>(1)
111669149778,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,require_once,<empty>,,true,,,require_once,,,0,
111669149779,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<operator>.coalesce,<empty>,,true,,,<operator>.coalesce,,,0,
111669149780,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$handler->processRequest,<empty>,,true,,,processRequest,,,0,<unresolvedSignature>(2)
111669149781,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$analyzer->performAnalysis,<empty>,,true,,,performAnalysis,,,0,<unresolvedSignature>(2)
111669149782,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,<unresolvedNamespace>\\$processor->execute,<empty>,,true,,,execute,,,0,<unresolvedSignature>(2)
111669149783,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,php_sapi_name,<empty>,,true,,,php_sapi_name,,,0,<unresolvedSignature>(0)
111669149784,METHOD,<global>,NAMESPACE_BLOCK,<empty>,,,<empty>,echo,<empty>,,true,,,echo,,,0,
111669149785,METHOD,<speculatedMethods>,NAMESPACE_BLOCK,<empty>,,,<empty>,array-><indexAccess>->analyze,<empty>,,true,,,analyze,,,0,
111669149786,METHOD,<speculatedMethods>,NAMESPACE_BLOCK,<empty>,,,<empty>,array-><returnValue>-><indexAccess>->analyze,<empty>,,true,,,analyze,,,0,
