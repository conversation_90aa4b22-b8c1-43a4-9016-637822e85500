47244640256,CONTROL_STRUCTURE,-1,,if (isset($this->processing<PERSON>hain[$target])),,IF,25,,,2,PhpIfStmt
47244640257,CONTROL_STRUCTURE,-1,,"if (empty($params) || strpos($params,""/"") !== false)",,IF,51,,,1,PhpIfStmt
47244640258,CONTROL_STRUCTURE,-1,,"if (in_array($target,$safeTargets))",,IF,75,,,4,PhpIfStmt
47244640259,CONTROL_STRUCTURE,-1,,if (isset($this->analysisModules[$target])),,IF,103,,,2,PhpIfStmt
47244640260,CONTROL_STRUCTURE,-1,,if (isset($this->transformationMap[$target])),,IF,31,,,2,PhpIfStmt
47244640261,CONTROL_STRUCTURE,-1,,if ($this->isSecureTarget($target)),,IF,38,,,3,PhpIfStmt
47244640262,CONTROL_STRUCTURE,-1,,"if (strpos($target,""safe_"") !== 0)",,IF,56,,,5,PhpIfStmt
47244640263,CONTROL_STRUCTURE,-1,,"if (strpos($target,""safe_bypass_"") !== 0)",,IF,62,,,6,PhpIfStmt
47244640264,CONTROL_STRUCTURE,-1,,foreach ($dangerousKeywords as $keyword),,FOR,64,,,2,PhpForeachStmt
47244640265,CONTROL_STRUCTURE,-1,,"if (strpos($target,$keyword) !== false)",,IF,65,,,1,PhpIfStmt
47244640266,CONTROL_STRUCTURE,-1,,if (strlen($target) > 100),,IF,75,,,7,PhpIfStmt
47244640267,CONTROL_STRUCTURE,-1,,"if (strpos($target,""safe_bypass_"") === 0)",,IF,81,,,8,PhpIfStmt
47244640268,CONTROL_STRUCTURE,-1,,if ($this->securityBypass),,IF,100,,,3,PhpIfStmt
47244640269,CONTROL_STRUCTURE,-1,,"if (strpos($actualFunction,""bypass_"") === 0)",,IF,104,,,1,PhpIfStmt
47244640270,CONTROL_STRUCTURE,-1,,if (function_exists($actualFunction)),,IF,110,,,2,PhpIfStmt
47244640271,CONTROL_STRUCTURE,-1,,foreach ($allowedPrefixes as $prefix),,FOR,14,,,8,PhpForeachStmt
47244640272,CONTROL_STRUCTURE,-1,,"if (strpos($functionName,$prefix) === 0)",,IF,15,,,1,PhpIfStmt
47244640273,CONTROL_STRUCTURE,-1,,break,,BREAK,17,,,2,PhpBreakStmt
47244640274,CONTROL_STRUCTURE,-1,,if (!$hasValidPrefix),,IF,21,,,9,PhpIfStmt
47244640275,CONTROL_STRUCTURE,-1,,foreach ($blacklist as $blocked),,FOR,38,,,6,PhpForeachStmt
47244640276,CONTROL_STRUCTURE,-1,,"if (strpos($target,$blocked) !== false)",,IF,39,,,1,PhpIfStmt
47244640277,CONTROL_STRUCTURE,-1,,"if (strlen($target) > 50 || !preg_match(""/^[a-zA-Z0-9_]+$/"",$target))",,IF,46,,,7,PhpIfStmt
47244640278,CONTROL_STRUCTURE,-1,,if (isset($functionMap[$funcName])),,IF,71,,,5,PhpIfStmt
47244640279,CONTROL_STRUCTURE,-1,,if (function_exists($realFunc)),,IF,75,,,2,PhpIfStmt
47244640280,CONTROL_STRUCTURE,-1,,switch ($type),,SWITCH,90,,,2,PhpSwitchStmt
47244640281,CONTROL_STRUCTURE,-1,,if (empty($filename)),,IF,106,,,1,PhpIfStmt
47244640282,CONTROL_STRUCTURE,-1,,"if (strpos($filename,"".."") !== false)",,IF,111,,,2,PhpIfStmt
47244640283,CONTROL_STRUCTURE,-1,,if (file_exists($filename) && is_readable($filename)),,IF,115,,,3,PhpIfStmt
47244640284,CONTROL_STRUCTURE,-1,,switch ($operation),,SWITCH,17,,,8,PhpSwitchStmt
47244640285,CONTROL_STRUCTURE,-1,,if (empty($target)),,IF,37,,,1,PhpIfStmt
47244640286,CONTROL_STRUCTURE,-1,,if (!isValidTarget($target)),,IF,52,,,3,PhpIfStmt
47244640287,CONTROL_STRUCTURE,-1,,"if (strpos($target,""safe_"") !== 0 && strpos($target,""transform_"") !== 0)",,IF,78,,,3,PhpIfStmt
47244640288,CONTROL_STRUCTURE,-1,,"if (php_sapi_name() === ""cli"" || isset($_GET[""op""]))",,IF,90,,,10,PhpIfStmt
