30064771072,CALL,-1,,$this->processing<PERSON>hain = ,,STATIC_DISPATCH,,7,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771073,CALL,1,,$this->processingChain,,STATIC_DISPATCH,,7,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771074,CALL,-1,,$DataHandler@tmp-0 = array(),,STATIC_DISPATCH,,7,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771075,CALL,2,,array(),,STATIC_DISPATCH,array,7,array,array,,,2,,array(),array
30064771076,CALL,-1,,$this->processingChain = ,,STATIC_DISPATCH,,10,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771077,CALL,1,,$this->processing<PERSON>hain,,STATIC_DISPATCH,,10,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771078,CALL,-1,,$<PERSON>Handler->__construct@tmp-0 = array(),,STATIC_DISPATCH,,10,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771079,CALL,2,,array(),,STATIC_DISPATCH,array,10,array,array,,,2,,array(),array
30064771080,CALL,-1,,"$DataHandler->__construct@tmp-0[""data""] = ""handleDataProcessing""",,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771081,CALL,1,,"$DataHandler->__construct@tmp-0[""data""]",,STATIC_DISPATCH,,11,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771082,CALL,-1,,"$DataHandler->__construct@tmp-0[""file""] = ""handleFileProcessing""",,STATIC_DISPATCH,,12,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771083,CALL,1,,"$DataHandler->__construct@tmp-0[""file""]",,STATIC_DISPATCH,,12,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771084,CALL,-1,,"$DataHandler->__construct@tmp-0[""cache""] = ""handleCacheProcessing""",,STATIC_DISPATCH,,13,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771085,CALL,1,,"$DataHandler->__construct@tmp-0[""cache""]",,STATIC_DISPATCH,,13,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771086,CALL,-1,,"$DataHandler->__construct@tmp-0[""log""] = ""handleLogProcessing""",,STATIC_DISPATCH,,14,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771087,CALL,1,,"$DataHandler->__construct@tmp-0[""log""]",,STATIC_DISPATCH,,14,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771088,CALL,-1,,isset($this->processingChain[$target]),,STATIC_DISPATCH,isset,25,isset,isset,,,1,,,bool
30064771089,CALL,1,,$this->processingChain[$target],,STATIC_DISPATCH,,25,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771090,CALL,1,,$this->processingChain,,STATIC_DISPATCH,,25,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771091,CALL,-1,,$method = $this->processingChain[$target],,STATIC_DISPATCH,,26,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771092,CALL,2,,$this->processingChain[$target],,STATIC_DISPATCH,,26,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771093,CALL,1,,$this->processingChain,,STATIC_DISPATCH,,26,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771094,CALL,1,,$this->$method($params),,DYNAMIC_DISPATCH,,30,<unresolvedNamespace>\\$this->$method,$method,,,1,,<unresolvedSignature>(1),ANY
30064771095,CALL,0,,$this->$method,,STATIC_DISPATCH,,30,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771096,CALL,1,,"$this->handleGenericProcessing($target,$params)",,DYNAMIC_DISPATCH,,34,DataHandler->handleGenericProcessing,handleGenericProcessing,,,1,,<unresolvedSignature>(2),ANY
30064771097,CALL,1,,"""Data processed: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,42,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771098,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,42,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771099,CALL,-1,,"empty($params) || strpos($params,""/"") !== false",,STATIC_DISPATCH,,51,<operator>.logicalOr,<operator>.logicalOr,,,1,,,ANY
30064771100,CALL,1,,empty($params),,STATIC_DISPATCH,empty,51,empty,empty,,,1,,,bool
30064771101,CALL,2,,"strpos($params,""/"") !== false",,STATIC_DISPATCH,,51,<operator>.notIdentical,<operator>.notIdentical,,,2,,,ANY
30064771102,CALL,1,,"strpos($params,""/"")",,STATIC_DISPATCH,strpos,51,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771103,CALL,1,,"""File processing result for: "" . $params . "" (safe)""",,STATIC_DISPATCH,encaps,55,encaps,encaps,,,1,,,string
30064771104,CALL,1,,"""Cache operation completed: "" . $params",,STATIC_DISPATCH,encaps,63,encaps,encaps,,,1,,,string
30064771105,CALL,-1,,$safeTargets = ,,STATIC_DISPATCH,,73,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771106,CALL,-1,,$DataHandler->handleGenericProcessing@tmp-0 = array(),,STATIC_DISPATCH,,73,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771107,CALL,2,,array(),,STATIC_DISPATCH,array,73,array,array,,,2,,array(),array
30064771108,CALL,-1,,"$DataHandler->handleGenericProcessing@tmp-0[0] = ""info""",,STATIC_DISPATCH,,73,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771109,CALL,1,,$DataHandler->handleGenericProcessing@tmp-0[0],,STATIC_DISPATCH,,73,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771110,CALL,-1,,"$DataHandler->handleGenericProcessing@tmp-0[1] = ""status""",,STATIC_DISPATCH,,73,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771111,CALL,1,,$DataHandler->handleGenericProcessing@tmp-0[1],,STATIC_DISPATCH,,73,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771112,CALL,-1,,"$DataHandler->handleGenericProcessing@tmp-0[2] = ""health""",,STATIC_DISPATCH,,73,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771113,CALL,1,,$DataHandler->handleGenericProcessing@tmp-0[2],,STATIC_DISPATCH,,73,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771114,CALL,-1,,"$DataHandler->handleGenericProcessing@tmp-0[3] = ""version""",,STATIC_DISPATCH,,73,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771115,CALL,1,,$DataHandler->handleGenericProcessing@tmp-0[3],,STATIC_DISPATCH,,73,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771116,CALL,-1,,"in_array($target,$safeTargets)",,STATIC_DISPATCH,in_array,75,in_array,in_array,,,1,,<unresolvedSignature>(2),ANY
30064771117,CALL,1,,"""Generic result for "" . $target . "": "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,76,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771118,CALL,1,,"""Generic result for "" . $target . "": """,,STATIC_DISPATCH,encaps,76,encaps,encaps,,,1,,,string
30064771119,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,76,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771120,CALL,1,,"""Target not supported in generic handler: "" . $target",,STATIC_DISPATCH,encaps,79,encaps,encaps,,,1,,,string
30064771121,CALL,-1,,$this->analysisModules = ,,STATIC_DISPATCH,,90,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771122,CALL,1,,$this->analysisModules,,STATIC_DISPATCH,,90,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771123,CALL,-1,,$SystemAnalyzer->__construct@tmp-0 = array(),,STATIC_DISPATCH,,90,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771124,CALL,2,,array(),,STATIC_DISPATCH,array,90,array,array,,,2,,array(),array
30064771125,CALL,-1,,"$SystemAnalyzer->__construct@tmp-0[""performance""] = ",,STATIC_DISPATCH,,91,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771126,CALL,1,,"$SystemAnalyzer->__construct@tmp-0[""performance""]",,STATIC_DISPATCH,,91,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771127,CALL,-1,,$SystemAnalyzer->__construct@tmp-1 = PerformanceAnalyzer.<alloc>(),,STATIC_DISPATCH,,91,<operator>.assignment,<operator>.assignment,,,1,,,PerformanceAnalyzer
30064771128,CALL,2,,PerformanceAnalyzer.<alloc>(),,STATIC_DISPATCH,,91,<operator>.alloc,<operator>.alloc,,,2,,,PerformanceAnalyzer
30064771129,CALL,-1,,PerformanceAnalyzer->__construct(),,DYNAMIC_DISPATCH,PerformanceAnalyzer->__construct,91,PerformanceAnalyzer->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771130,CALL,-1,,"$SystemAnalyzer->__construct@tmp-0[""security""] = ",,STATIC_DISPATCH,,92,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771131,CALL,1,,"$SystemAnalyzer->__construct@tmp-0[""security""]",,STATIC_DISPATCH,,92,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771132,CALL,-1,,$SystemAnalyzer->__construct@tmp-2 = SecurityAnalyzer.<alloc>(),,STATIC_DISPATCH,,92,<operator>.assignment,<operator>.assignment,,,1,,,SecurityAnalyzer
30064771133,CALL,2,,SecurityAnalyzer.<alloc>(),,STATIC_DISPATCH,,92,<operator>.alloc,<operator>.alloc,,,2,,,SecurityAnalyzer
30064771134,CALL,-1,,SecurityAnalyzer->__construct(),,DYNAMIC_DISPATCH,SecurityAnalyzer->__construct,92,SecurityAnalyzer->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771135,CALL,-1,,"$SystemAnalyzer->__construct@tmp-0[""resource""] = ",,STATIC_DISPATCH,,93,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771136,CALL,1,,"$SystemAnalyzer->__construct@tmp-0[""resource""]",,STATIC_DISPATCH,,93,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771137,CALL,-1,,$SystemAnalyzer->__construct@tmp-3 = ResourceAnalyzer.<alloc>(),,STATIC_DISPATCH,,93,<operator>.assignment,<operator>.assignment,,,1,,,ResourceAnalyzer
30064771138,CALL,2,,ResourceAnalyzer.<alloc>(),,STATIC_DISPATCH,,93,<operator>.alloc,<operator>.alloc,,,2,,,ResourceAnalyzer
30064771139,CALL,-1,,ResourceAnalyzer->__construct(),,DYNAMIC_DISPATCH,ResourceAnalyzer->__construct,93,ResourceAnalyzer->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771140,CALL,-1,,isset($this->analysisModules[$target]),,STATIC_DISPATCH,isset,103,isset,isset,,,1,,,bool
30064771141,CALL,1,,$this->analysisModules[$target],,STATIC_DISPATCH,,103,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771142,CALL,1,,$this->analysisModules,,STATIC_DISPATCH,,103,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771143,CALL,-1,,$analyzer = $this->analysisModules[$target],,STATIC_DISPATCH,,104,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771144,CALL,2,,$this->analysisModules[$target],,STATIC_DISPATCH,,104,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771145,CALL,1,,$this->analysisModules,,STATIC_DISPATCH,,104,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771146,CALL,1,,$analyzer->analyze($params),,DYNAMIC_DISPATCH,array-><indexAccess>->analyze;array-><returnValue>-><indexAccess>->analyze,105,<unresolvedNamespace>\\$analyzer->analyze,analyze,,,1,,<unresolvedSignature>(1),ANY
30064771147,CALL,1,,"$this->performBasicAnalysis($target,$params)",,DYNAMIC_DISPATCH,,108,SystemAnalyzer->performBasicAnalysis,performBasicAnalysis,,,1,,<unresolvedSignature>(2),ANY
30064771148,CALL,1,,"""Basic analysis completed for "" . $target . "" with params: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,116,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771149,CALL,1,,"""Basic analysis completed for "" . $target . "" with params: """,,STATIC_DISPATCH,encaps,116,encaps,encaps,,,1,,,string
30064771150,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,116,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771151,CALL,1,,"""Analysis report for "" . $type . "" generated safely""",,STATIC_DISPATCH,encaps,124,encaps,encaps,,,1,,,string
30064771152,CALL,1,,"""Component "" . $component . "" status: OK""",,STATIC_DISPATCH,encaps,132,encaps,encaps,,,1,,,string
30064771153,CALL,1,,"""Performance metrics: CPU OK, Memory OK, Params: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,142,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771154,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,142,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771155,CALL,1,,"""Security scan completed, no threats detected. Params: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,152,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771156,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,152,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771157,CALL,1,,"""Resource utilization normal. Params: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,162,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771158,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,162,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771159,CALL,-1,,$this->securityBypass = false,,STATIC_DISPATCH,,9,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771160,CALL,1,,$this->securityBypass,,STATIC_DISPATCH,,9,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771161,CALL,-1,,$this->transformationMap = ,,STATIC_DISPATCH,,13,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771162,CALL,1,,$this->transformationMap,,STATIC_DISPATCH,,13,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771163,CALL,-1,,$CoreProcessor->__construct@tmp-0 = array(),,STATIC_DISPATCH,,13,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771164,CALL,2,,array(),,STATIC_DISPATCH,array,13,array,array,,,2,,array(),array
30064771165,CALL,-1,,"$CoreProcessor->__construct@tmp-0[""safe_demo""] = ""performSafeDemo""",,STATIC_DISPATCH,,14,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771166,CALL,1,,"$CoreProcessor->__construct@tmp-0[""safe_demo""]",,STATIC_DISPATCH,,14,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771167,CALL,-1,,"$CoreProcessor->__construct@tmp-0[""safe_info""] = ""performSafeInfo""",,STATIC_DISPATCH,,15,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771168,CALL,1,,"$CoreProcessor->__construct@tmp-0[""safe_info""]",,STATIC_DISPATCH,,15,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771169,CALL,-1,,"$CoreProcessor->__construct@tmp-0[""safe_format""] = ""performSafeFormat""",,STATIC_DISPATCH,,16,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771170,CALL,1,,"$CoreProcessor->__construct@tmp-0[""safe_format""]",,STATIC_DISPATCH,,16,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771171,CALL,-1,,"$CoreProcessor->__construct@tmp-0[""transform_data""] = ""transformData""",,STATIC_DISPATCH,,17,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771172,CALL,1,,"$CoreProcessor->__construct@tmp-0[""transform_data""]",,STATIC_DISPATCH,,17,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771173,CALL,-1,,"$CoreProcessor->__construct@tmp-0[""transform_string""] = ""transformString""",,STATIC_DISPATCH,,18,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771174,CALL,1,,"$CoreProcessor->__construct@tmp-0[""transform_string""]",,STATIC_DISPATCH,,18,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771175,CALL,-1,,"$CoreProcessor->__construct@tmp-0[""transform_array""] = ""transformArray""",,STATIC_DISPATCH,,19,<operator>.assignment,<operator>.assignment,,,7,,,ANY
30064771176,CALL,1,,"$CoreProcessor->__construct@tmp-0[""transform_array""]",,STATIC_DISPATCH,,19,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771177,CALL,-1,,isset($this->transformationMap[$target]),,STATIC_DISPATCH,isset,31,isset,isset,,,1,,,bool
30064771178,CALL,1,,$this->transformationMap[$target],,STATIC_DISPATCH,,31,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771179,CALL,1,,$this->transformationMap,,STATIC_DISPATCH,,31,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771180,CALL,-1,,$method = $this->transformationMap[$target],,STATIC_DISPATCH,,32,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771181,CALL,2,,$this->transformationMap[$target],,STATIC_DISPATCH,,32,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771182,CALL,1,,$this->transformationMap,,STATIC_DISPATCH,,32,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771183,CALL,1,,$this->$method($params),,DYNAMIC_DISPATCH,,34,<unresolvedNamespace>\\$this->$method,$method,,,1,,<unresolvedSignature>(1),ANY
30064771184,CALL,0,,$this->$method,,STATIC_DISPATCH,,34,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771185,CALL,-1,,$this->isSecureTarget($target),,DYNAMIC_DISPATCH,,38,CoreProcessor->isSecureTarget,isSecureTarget,,,1,,<unresolvedSignature>(1),bool
30064771186,CALL,1,,"$this->processSecureTarget($target,$params)",,DYNAMIC_DISPATCH,,42,CoreProcessor->processSecureTarget,processSecureTarget,,,1,,<unresolvedSignature>(2),ANY
30064771187,CALL,1,,"""Target not allowed by security policy: "" . $target",,STATIC_DISPATCH,encaps,45,encaps,encaps,,,1,,,string
30064771188,CALL,-1,,"strpos($target,""safe_"") !== 0",,STATIC_DISPATCH,,56,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771189,CALL,1,,"strpos($target,""safe_"")",,STATIC_DISPATCH,strpos,56,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771190,CALL,-1,,"strpos($target,""safe_bypass_"") !== 0",,STATIC_DISPATCH,,62,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771191,CALL,1,,"strpos($target,""safe_bypass_"")",,STATIC_DISPATCH,strpos,62,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771192,CALL,-1,,$dangerousKeywords = ,,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771193,CALL,-1,,$CoreProcessor->isSecureTarget@tmp-0 = array(),,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771194,CALL,2,,array(),,STATIC_DISPATCH,array,63,array,array,,,2,,array(),array
30064771195,CALL,-1,,"$CoreProcessor->isSecureTarget@tmp-0[0] = ""exec""",,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771196,CALL,1,,$CoreProcessor->isSecureTarget@tmp-0[0],,STATIC_DISPATCH,,63,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771197,CALL,-1,,"$CoreProcessor->isSecureTarget@tmp-0[1] = ""system""",,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771198,CALL,1,,$CoreProcessor->isSecureTarget@tmp-0[1],,STATIC_DISPATCH,,63,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771199,CALL,-1,,"$CoreProcessor->isSecureTarget@tmp-0[2] = ""shell""",,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771200,CALL,1,,$CoreProcessor->isSecureTarget@tmp-0[2],,STATIC_DISPATCH,,63,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771201,CALL,-1,,"$CoreProcessor->isSecureTarget@tmp-0[3] = ""eval""",,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771202,CALL,1,,$CoreProcessor->isSecureTarget@tmp-0[3],,STATIC_DISPATCH,,63,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771203,CALL,-1,,"$CoreProcessor->isSecureTarget@tmp-0[4] = ""file_get_contents""",,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771204,CALL,1,,$CoreProcessor->isSecureTarget@tmp-0[4],,STATIC_DISPATCH,,63,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771205,CALL,-1,,$CoreProcessor->isSecureTarget@iter_tmp-1 = $dangerousKeywords,,STATIC_DISPATCH,,64,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771206,CALL,-1,,$keyword = $CoreProcessor->isSecureTarget@iter_tmp-1->current(),,STATIC_DISPATCH,,64,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771207,CALL,2,,$CoreProcessor->isSecureTarget@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,64,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771208,CALL,-1,,!is_null($keyword),,STATIC_DISPATCH,,64,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771209,CALL,1,,is_null($keyword),,STATIC_DISPATCH,is_null,64,is_null,is_null,,,1,,,bool
30064771210,CALL,-1,,$CoreProcessor->isSecureTarget@iter_tmp-1->next(),,DYNAMIC_DISPATCH,Iterator.next,64,Iterator.next,next,,,1,,void(),ANY
30064771211,CALL,-1,,$keyword = $CoreProcessor->isSecureTarget@iter_tmp-1->current(),,STATIC_DISPATCH,,64,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771212,CALL,2,,$CoreProcessor->isSecureTarget@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,64,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771213,CALL,-1,,"strpos($target,$keyword) !== false",,STATIC_DISPATCH,,65,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771214,CALL,1,,"strpos($target,$keyword)",,STATIC_DISPATCH,strpos,65,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771215,CALL,-1,,strlen($target) > 100,,STATIC_DISPATCH,,75,<operator>.greaterThan,<operator>.greaterThan,,,1,,,ANY
30064771216,CALL,1,,strlen($target),,STATIC_DISPATCH,strlen,75,strlen,strlen,,,1,,<unresolvedSignature>(1),ANY
30064771217,CALL,-1,,"strpos($target,""safe_bypass_"") === 0",,STATIC_DISPATCH,,81,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771218,CALL,1,,"strpos($target,""safe_bypass_"")",,STATIC_DISPATCH,strpos,81,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771219,CALL,-1,,$this->securityBypass = true,,STATIC_DISPATCH,,83,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771220,CALL,1,,$this->securityBypass,,STATIC_DISPATCH,,83,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771221,CALL,-1,,"$actualFunction = substr($target,5)",,STATIC_DISPATCH,,97,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771222,CALL,2,,"substr($target,5)",,STATIC_DISPATCH,substr,97,substr,substr,,,2,,<unresolvedSignature>(2),ANY
30064771223,CALL,-1,,$this->securityBypass,,STATIC_DISPATCH,,100,<operator>.fieldAccess,<operator>.fieldAccess,,,1,,,ANY
30064771224,CALL,-1,,"strpos($actualFunction,""bypass_"") === 0",,STATIC_DISPATCH,,104,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771225,CALL,1,,"strpos($actualFunction,""bypass_"")",,STATIC_DISPATCH,strpos,104,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771226,CALL,-1,,"$actualFunction = substr($actualFunction,7)",,STATIC_DISPATCH,,105,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771227,CALL,2,,"substr($actualFunction,7)",,STATIC_DISPATCH,substr,105,substr,substr,,,2,,<unresolvedSignature>(2),ANY
30064771228,CALL,-1,,function_exists($actualFunction),,STATIC_DISPATCH,function_exists,110,function_exists,function_exists,,,1,,<unresolvedSignature>(1),ANY
30064771229,CALL,1,,"call_user_func($actualFunction,$params)",,STATIC_DISPATCH,call_user_func,113,call_user_func,call_user_func,,,1,,<unresolvedSignature>(2),ANY
30064771230,CALL,1,,"""Function does not exist: "" . $actualFunction",,STATIC_DISPATCH,encaps,115,encaps,encaps,,,1,,,string
30064771231,CALL,1,,"$this->performDefaultProcessing($actualFunction,$params)",,DYNAMIC_DISPATCH,,121,CoreProcessor->performDefaultProcessing,performDefaultProcessing,,,1,,<unresolvedSignature>(2),ANY
30064771232,CALL,1,,"""Default processing completed for "" . $funcName . "" with params: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,129,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771233,CALL,1,,"""Default processing completed for "" . $funcName . "" with params: """,,STATIC_DISPATCH,encaps,129,encaps,encaps,,,1,,,string
30064771234,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,129,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771235,CALL,1,,"""Safe demo executed with: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,136,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771236,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,136,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771237,CALL,1,,"""Safe info: PHP "" . PHP_VERSION . "", Params: "" . htmlspecialchars($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,143,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771238,CALL,1,,"""Safe info: PHP "" . PHP_VERSION . "", Params: """,,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,143,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771239,CALL,1,,"""Safe info: PHP "" . PHP_VERSION",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,143,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771240,CALL,2,,PHP_VERSION,,STATIC_DISPATCH,,143,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771241,CALL,2,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,143,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771242,CALL,1,,"""Safely formatted: "" . strtoupper(htmlspecialchars($params))",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,150,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771243,CALL,2,,strtoupper(htmlspecialchars($params)),,STATIC_DISPATCH,strtoupper,150,strtoupper,strtoupper,,,2,,<unresolvedSignature>(1),ANY
30064771244,CALL,1,,htmlspecialchars($params),,STATIC_DISPATCH,htmlspecialchars,150,htmlspecialchars,htmlspecialchars,,,1,,<unresolvedSignature>(1),ANY
30064771245,CALL,1,,"""Data transformed: "" . base64_encode($params)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>,157,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771246,CALL,2,,base64_encode($params),,STATIC_DISPATCH,base64_encode,157,base64_encode,base64_encode,,,2,,<unresolvedSignature>(1),ANY
30064771247,CALL,-1,,$allowedPrefixes = ,,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771248,CALL,-1,,$delegateToUtility@tmp-0 = array(),,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771249,CALL,2,,array(),,STATIC_DISPATCH,array,11,array,array,,,2,,array(),array
30064771250,CALL,-1,,"$delegateToUtility@tmp-0[0] = ""info_""",,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771251,CALL,1,,$delegateToUtility@tmp-0[0],,STATIC_DISPATCH,,11,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771252,CALL,-1,,"$delegateToUtility@tmp-0[1] = ""util_""",,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771253,CALL,1,,$delegateToUtility@tmp-0[1],,STATIC_DISPATCH,,11,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771254,CALL,-1,,"$delegateToUtility@tmp-0[2] = ""helper_""",,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771255,CALL,1,,$delegateToUtility@tmp-0[2],,STATIC_DISPATCH,,11,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771256,CALL,-1,,"$delegateToUtility@tmp-0[3] = ""safe_""",,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771257,CALL,1,,$delegateToUtility@tmp-0[3],,STATIC_DISPATCH,,11,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771258,CALL,-1,,$hasValidPrefix = false,,STATIC_DISPATCH,,12,<operator>.assignment,<operator>.assignment,,,7,,,ANY
30064771259,CALL,-1,,$delegateToUtility@iter_tmp-1 = $allowedPrefixes,,STATIC_DISPATCH,,14,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771260,CALL,-1,,$prefix = $delegateToUtility@iter_tmp-1->current(),,STATIC_DISPATCH,,14,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771261,CALL,2,,$delegateToUtility@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,14,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771262,CALL,-1,,!is_null($prefix),,STATIC_DISPATCH,,14,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771263,CALL,1,,is_null($prefix),,STATIC_DISPATCH,is_null,14,is_null,is_null,,,1,,,bool
30064771264,CALL,-1,,$delegateToUtility@iter_tmp-1->next(),,DYNAMIC_DISPATCH,Iterator.next,14,Iterator.next,next,,,1,,void(),ANY
30064771265,CALL,-1,,$prefix = $delegateToUtility@iter_tmp-1->current(),,STATIC_DISPATCH,,14,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771266,CALL,2,,$delegateToUtility@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,14,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771267,CALL,-1,,"strpos($functionName,$prefix) === 0",,STATIC_DISPATCH,,15,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771268,CALL,1,,"strpos($functionName,$prefix)",,STATIC_DISPATCH,strpos,15,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771269,CALL,-1,,$hasValidPrefix = true,,STATIC_DISPATCH,,16,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771270,CALL,-1,,!$hasValidPrefix,,STATIC_DISPATCH,,21,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771271,CALL,1,,"""Function prefix not allowed: "" . $functionName",,STATIC_DISPATCH,encaps,22,encaps,encaps,,,1,,,string
30064771272,CALL,1,,"invokeUtilityFunction($functionName,$params)",,STATIC_DISPATCH,invokeUtilityFunction,26,invokeUtilityFunction,invokeUtilityFunction,,,1,,<unresolvedSignature>(2),ANY
30064771273,CALL,-1,,$blacklist = ,,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771274,CALL,-1,,$isValidTarget@tmp-0 = array(),,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771275,CALL,2,,array(),,STATIC_DISPATCH,array,36,array,array,,,2,,array(),array
30064771276,CALL,-1,,"$isValidTarget@tmp-0[0] = ""system""",,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771277,CALL,1,,$isValidTarget@tmp-0[0],,STATIC_DISPATCH,,36,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771278,CALL,-1,,"$isValidTarget@tmp-0[1] = ""exec""",,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771279,CALL,1,,$isValidTarget@tmp-0[1],,STATIC_DISPATCH,,36,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771280,CALL,-1,,"$isValidTarget@tmp-0[2] = ""shell""",,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771281,CALL,1,,$isValidTarget@tmp-0[2],,STATIC_DISPATCH,,36,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771282,CALL,-1,,"$isValidTarget@tmp-0[3] = ""eval""",,STATIC_DISPATCH,,36,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771283,CALL,1,,$isValidTarget@tmp-0[3],,STATIC_DISPATCH,,36,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771284,CALL,-1,,$isValidTarget@iter_tmp-1 = $blacklist,,STATIC_DISPATCH,,38,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771285,CALL,-1,,$blocked = $isValidTarget@iter_tmp-1->current(),,STATIC_DISPATCH,,38,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771286,CALL,2,,$isValidTarget@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,38,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771287,CALL,-1,,!is_null($blocked),,STATIC_DISPATCH,,38,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771288,CALL,1,,is_null($blocked),,STATIC_DISPATCH,is_null,38,is_null,is_null,,,1,,,bool
30064771289,CALL,-1,,$isValidTarget@iter_tmp-1->next(),,DYNAMIC_DISPATCH,Iterator.next,38,Iterator.next,next,,,1,,void(),ANY
30064771290,CALL,-1,,$blocked = $isValidTarget@iter_tmp-1->current(),,STATIC_DISPATCH,,38,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771291,CALL,2,,$isValidTarget@iter_tmp-1->current(),,DYNAMIC_DISPATCH,Iterator.current,38,Iterator.current,current,,,2,,<unresolvedSignature>(0),ANY
30064771292,CALL,-1,,"strpos($target,$blocked) !== false",,STATIC_DISPATCH,,39,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771293,CALL,1,,"strpos($target,$blocked)",,STATIC_DISPATCH,strpos,39,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771294,CALL,-1,,"strlen($target) > 50 || !preg_match(""/^[a-zA-Z0-9_]+$/"",$target)",,STATIC_DISPATCH,,46,<operator>.logicalOr,<operator>.logicalOr,,,1,,,ANY
30064771295,CALL,1,,strlen($target) > 50,,STATIC_DISPATCH,,46,<operator>.greaterThan,<operator>.greaterThan,,,1,,,ANY
30064771296,CALL,1,,strlen($target),,STATIC_DISPATCH,strlen,46,strlen,strlen,,,1,,<unresolvedSignature>(1),ANY
30064771297,CALL,2,,"!preg_match(""/^[a-zA-Z0-9_]+$/"",$target)",,STATIC_DISPATCH,,46,<operator>.logicalNot,<operator>.logicalNot,,,2,,,ANY
30064771298,CALL,1,,"preg_match(""/^[a-zA-Z0-9_]+$/"",$target)",,STATIC_DISPATCH,preg_match,46,preg_match,preg_match,,,1,,<unresolvedSignature>(2),ANY
30064771299,CALL,-1,,$functionMap = ,,STATIC_DISPATCH,,61,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771300,CALL,-1,,$invokeUtilityFunction@tmp-0 = array(),,STATIC_DISPATCH,,61,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771301,CALL,2,,array(),,STATIC_DISPATCH,array,61,array,array,,,2,,array(),array
30064771302,CALL,-1,,"$invokeUtilityFunction@tmp-0[""info_system""] = ""getSystemInformation""",,STATIC_DISPATCH,,62,<operator>.assignment,<operator>.assignment,,,2,,,ANY
30064771303,CALL,1,,"$invokeUtilityFunction@tmp-0[""info_system""]",,STATIC_DISPATCH,,62,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771304,CALL,-1,,"$invokeUtilityFunction@tmp-0[""info_file""] = ""getFileInformation""",,STATIC_DISPATCH,,63,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771305,CALL,1,,"$invokeUtilityFunction@tmp-0[""info_file""]",,STATIC_DISPATCH,,63,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771306,CALL,-1,,"$invokeUtilityFunction@tmp-0[""info_network""] = ""getNetworkInformation""",,STATIC_DISPATCH,,64,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771307,CALL,1,,"$invokeUtilityFunction@tmp-0[""info_network""]",,STATIC_DISPATCH,,64,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771308,CALL,-1,,"$invokeUtilityFunction@tmp-0[""util_format""] = ""formatData""",,STATIC_DISPATCH,,65,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771309,CALL,1,,"$invokeUtilityFunction@tmp-0[""util_format""]",,STATIC_DISPATCH,,65,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771310,CALL,-1,,"$invokeUtilityFunction@tmp-0[""helper_validate""] = ""validateInput""",,STATIC_DISPATCH,,66,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771311,CALL,1,,"$invokeUtilityFunction@tmp-0[""helper_validate""]",,STATIC_DISPATCH,,66,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771312,CALL,-1,,"$invokeUtilityFunction@tmp-0[""safe_process""] = ""processSafely""",,STATIC_DISPATCH,,67,<operator>.assignment,<operator>.assignment,,,7,,,ANY
30064771313,CALL,1,,"$invokeUtilityFunction@tmp-0[""safe_process""]",,STATIC_DISPATCH,,67,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771314,CALL,-1,,isset($functionMap[$funcName]),,STATIC_DISPATCH,isset,71,isset,isset,,,1,,,bool
30064771315,CALL,1,,$functionMap[$funcName],,STATIC_DISPATCH,,71,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771316,CALL,-1,,$realFunc = $functionMap[$funcName],,STATIC_DISPATCH,,72,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771317,CALL,2,,$functionMap[$funcName],,STATIC_DISPATCH,,72,<operator>.indexAccess,<operator>.indexAccess,,,2,,,ANY
30064771318,CALL,-1,,function_exists($realFunc),,STATIC_DISPATCH,function_exists,75,function_exists,function_exists,,,1,,<unresolvedSignature>(1),ANY
30064771319,CALL,1,,$realFunc($params),,STATIC_DISPATCH,$realFunc,76,$realFunc,$realFunc,,,1,,<unresolvedSignature>(1),ANY
30064771320,CALL,1,,"""Mapped function does not exist: "" . $realFunc",,STATIC_DISPATCH,encaps,78,encaps,encaps,,,1,,,string
30064771321,CALL,1,,"""Utility function not found in mapping: "" . $funcName",,STATIC_DISPATCH,encaps,83,encaps,encaps,,,1,,,string
30064771322,CALL,1,,"""PHP Version: "" . PHP_VERSION",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>;encaps-><returnValue>-><operator>.concat-><returnValue>-><operator>.concat-><returnValue>,92,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771323,CALL,2,,PHP_VERSION,,STATIC_DISPATCH,,92,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771324,CALL,1,,"""OS: "" . PHP_OS",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>;encaps-><returnValue>-><operator>.concat-><returnValue>-><operator>.concat-><returnValue>,94,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771325,CALL,2,,PHP_OS,,STATIC_DISPATCH,,94,<operator>.fieldAccess,<operator>.fieldAccess,,,2,,,ANY
30064771326,CALL,1,,"""Memory: "" . memory_get_usage() . "" bytes""",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>;encaps-><returnValue>-><operator>.concat-><returnValue>-><operator>.concat-><returnValue>,96,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771327,CALL,1,,"""Memory: "" . memory_get_usage()",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>;encaps-><returnValue>-><operator>.concat-><returnValue>-><operator>.concat-><returnValue>,96,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771328,CALL,2,,memory_get_usage(),,STATIC_DISPATCH,memory_get_usage,96,memory_get_usage,memory_get_usage,,,2,,<unresolvedSignature>(0),ANY
30064771329,CALL,1,,"""System info type: "" . $type . "" (safe)""",,STATIC_DISPATCH,encaps,98,encaps,encaps,,,1,,,string
30064771330,CALL,-1,,empty($filename),,STATIC_DISPATCH,empty,106,empty,empty,,,1,,,bool
30064771331,CALL,-1,,"strpos($filename,"".."") !== false",,STATIC_DISPATCH,,111,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771332,CALL,1,,"strpos($filename,"".."")",,STATIC_DISPATCH,strpos,111,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771333,CALL,-1,,file_exists($filename) && is_readable($filename),,STATIC_DISPATCH,,115,<operator>.logicalAnd,<operator>.logicalAnd,,,1,,,ANY
30064771334,CALL,1,,file_exists($filename),,STATIC_DISPATCH,file_exists,115,file_exists,file_exists,,,1,,<unresolvedSignature>(1),ANY
30064771335,CALL,2,,is_readable($filename),,STATIC_DISPATCH,is_readable,115,is_readable,is_readable,,,2,,<unresolvedSignature>(1),ANY
30064771336,CALL,1,,"""File exists: "" . $filename . "" ("" . filesize($filename) . "" bytes)""",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>;encaps-><returnValue>-><operator>.concat-><returnValue>-><operator>.concat-><returnValue>,116,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771337,CALL,1,,"""File exists: "" . $filename . "" ("" . filesize($filename)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>;encaps-><returnValue>-><operator>.concat-><returnValue>-><operator>.concat-><returnValue>,116,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771338,CALL,1,,"""File exists: "" . $filename . "" (""",,STATIC_DISPATCH,encaps,116,encaps,encaps,,,1,,,string
30064771339,CALL,2,,filesize($filename),,STATIC_DISPATCH,filesize,116,filesize,filesize,,,2,,<unresolvedSignature>(1),ANY
30064771340,CALL,1,,"""File not accessible: "" . $filename",,STATIC_DISPATCH,encaps,119,encaps,encaps,,,1,,,string
30064771341,CALL,1,,"""Network target: "" . $target . "" (simulated check - safe)""",,STATIC_DISPATCH,encaps,127,encaps,encaps,,,1,,,string
30064771342,CALL,1,,"""Formatted: "" . htmlspecialchars($data)",,STATIC_DISPATCH,encaps-><returnValue>-><operator>.concat-><returnValue>;encaps-><returnValue>-><operator>.concat-><returnValue>-><operator>.concat-><returnValue>,134,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771343,CALL,2,,htmlspecialchars($data),,STATIC_DISPATCH,htmlspecialchars,134,htmlspecialchars,htmlspecialchars,,,2,,<unresolvedSignature>(1),ANY
30064771344,CALL,-1,,"require_once ""functions.php""",,STATIC_DISPATCH,require_once,2,require_once,require_once,,,2,,,ANY
30064771345,CALL,-1,,"require_once ""Handler.php""",,STATIC_DISPATCH,require_once,3,require_once,require_once,,,3,,,ANY
30064771346,CALL,-1,,"require_once ""Processor.php""",,STATIC_DISPATCH,require_once,4,require_once,require_once,,,4,,,ANY
30064771347,CALL,-1,,"$operation = $_GET[""op""] ?? ""info""",,STATIC_DISPATCH,,10,<operator>.assignment,<operator>.assignment,,,5,,,ANY
30064771348,CALL,2,,"$_GET[""op""] ?? ""info""",,STATIC_DISPATCH,,10,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771349,CALL,1,,"$_GET[""op""]",,STATIC_DISPATCH,,10,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771350,CALL,-1,,"$target = $_GET[""target""] ?? """"",,STATIC_DISPATCH,,11,<operator>.assignment,<operator>.assignment,,,6,,,ANY
30064771351,CALL,2,,"$_GET[""target""] ?? """"",,STATIC_DISPATCH,,11,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771352,CALL,1,,"$_GET[""target""]",,STATIC_DISPATCH,,11,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771353,CALL,-1,,"$params = $_GET[""params""] ?? """"",,STATIC_DISPATCH,,12,<operator>.assignment,<operator>.assignment,,,7,,,ANY
30064771354,CALL,2,,"$_GET[""params""] ?? """"",,STATIC_DISPATCH,,12,<operator>.coalesce,<operator>.coalesce,,,2,,,ANY
30064771355,CALL,1,,"$_GET[""params""]",,STATIC_DISPATCH,,12,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771356,CALL,1,,"processInformation($target,$params)",,STATIC_DISPATCH,processInformation,19,processInformation,processInformation,,,1,,<unresolvedSignature>(2),ANY
30064771357,CALL,1,,"processManagement($target,$params)",,STATIC_DISPATCH,processManagement,21,processManagement,processManagement,,,1,,<unresolvedSignature>(2),ANY
30064771358,CALL,1,,"processAnalysis($target,$params)",,STATIC_DISPATCH,processAnalysis,23,processAnalysis,processAnalysis,,,1,,<unresolvedSignature>(2),ANY
30064771359,CALL,1,,"processTransformation($target,$params)",,STATIC_DISPATCH,processTransformation,25,processTransformation,processTransformation,,,1,,<unresolvedSignature>(2),ANY
30064771360,CALL,1,,"""Unknown operation: "" . $operation",,STATIC_DISPATCH,encaps,27,encaps,encaps,,,1,,,string
30064771361,CALL,-1,,empty($target),,STATIC_DISPATCH,empty,37,empty,empty,,,1,,,bool
30064771362,CALL,1,,"delegateToUtility(""info_"" . $target,$params)",,STATIC_DISPATCH,delegateToUtility,42,delegateToUtility,delegateToUtility,,,1,,<unresolvedSignature>(2),ANY
30064771363,CALL,1,,"""info_"" . $target",,STATIC_DISPATCH,,42,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771364,CALL,-1,,!isValidTarget($target),,STATIC_DISPATCH,,52,<operator>.logicalNot,<operator>.logicalNot,,,1,,,ANY
30064771365,CALL,1,,isValidTarget($target),,STATIC_DISPATCH,isValidTarget,52,isValidTarget,isValidTarget,,,1,,<unresolvedSignature>(1),ANY
30064771366,CALL,-1,,$handler = ,,STATIC_DISPATCH,,57,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771367,CALL,-1,,$processManagement@tmp-0 = DataHandler.<alloc>(),,STATIC_DISPATCH,,57,<operator>.assignment,<operator>.assignment,,,1,,,DataHandler
30064771368,CALL,2,,DataHandler.<alloc>(),,STATIC_DISPATCH,,57,<operator>.alloc,<operator>.alloc,,,2,,,DataHandler
30064771369,CALL,-1,,DataHandler->__construct(),,DYNAMIC_DISPATCH,DataHandler->__construct,57,DataHandler->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771370,CALL,1,,"$handler->processRequest($target,$params)",,DYNAMIC_DISPATCH,,58,DataHandler->processRequest,processRequest,,,1,,<unresolvedSignature>(2),DataHandler->processRequest-><returnValue>
30064771371,CALL,-1,,$analyzer = ,,STATIC_DISPATCH,,67,<operator>.assignment,<operator>.assignment,,,3,,,ANY
30064771372,CALL,-1,,$processAnalysis@tmp-0 = SystemAnalyzer.<alloc>(),,STATIC_DISPATCH,,67,<operator>.assignment,<operator>.assignment,,,1,,,SystemAnalyzer
30064771373,CALL,2,,SystemAnalyzer.<alloc>(),,STATIC_DISPATCH,,67,<operator>.alloc,<operator>.alloc,,,2,,,SystemAnalyzer
30064771374,CALL,-1,,SystemAnalyzer->__construct(),,DYNAMIC_DISPATCH,SystemAnalyzer->__construct,67,SystemAnalyzer->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771375,CALL,1,,"$analyzer->performAnalysis($target,$params)",,DYNAMIC_DISPATCH,,68,SystemAnalyzer->performAnalysis,performAnalysis,,,1,,<unresolvedSignature>(2),SystemAnalyzer->performAnalysis-><returnValue>
30064771376,CALL,-1,,"strpos($target,""safe_"") !== 0 && strpos($target,""transform_"") !== 0",,STATIC_DISPATCH,,78,<operator>.logicalAnd,<operator>.logicalAnd,,,1,,,ANY
30064771377,CALL,1,,"strpos($target,""safe_"") !== 0",,STATIC_DISPATCH,,78,<operator>.notIdentical,<operator>.notIdentical,,,1,,,ANY
30064771378,CALL,1,,"strpos($target,""safe_"")",,STATIC_DISPATCH,strpos,78,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771379,CALL,2,,"strpos($target,""transform_"") !== 0",,STATIC_DISPATCH,,78,<operator>.notIdentical,<operator>.notIdentical,,,2,,,ANY
30064771380,CALL,1,,"strpos($target,""transform_"")",,STATIC_DISPATCH,strpos,78,strpos,strpos,,,1,,<unresolvedSignature>(2),ANY
30064771381,CALL,-1,,"$target = ""safe_"" . $target",,STATIC_DISPATCH,,81,<operator>.assignment,<operator>.assignment,,,1,,,ANY
30064771382,CALL,2,,"""safe_"" . $target",,STATIC_DISPATCH,,81,<operator>.concat,<operator>.concat,,,2,,,ANY
30064771383,CALL,-1,,$processor = ,,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,4,,,ANY
30064771384,CALL,-1,,$processTransformation@tmp-0 = CoreProcessor.<alloc>(),,STATIC_DISPATCH,,85,<operator>.assignment,<operator>.assignment,,,1,,,CoreProcessor
30064771385,CALL,2,,CoreProcessor.<alloc>(),,STATIC_DISPATCH,,85,<operator>.alloc,<operator>.alloc,,,2,,,CoreProcessor
30064771386,CALL,-1,,CoreProcessor->__construct(),,DYNAMIC_DISPATCH,CoreProcessor->__construct,85,CoreProcessor->__construct,__construct,,,2,,<unresolvedSignature>(0),ANY
30064771387,CALL,1,,"$processor->execute($target,$params)",,DYNAMIC_DISPATCH,,86,CoreProcessor->execute,execute,,,1,,<unresolvedSignature>(2),CoreProcessor->execute-><returnValue>
30064771388,CALL,-1,,"php_sapi_name() === ""cli"" || isset($_GET[""op""])",,STATIC_DISPATCH,,90,<operator>.logicalOr,<operator>.logicalOr,,,1,,,ANY
30064771389,CALL,1,,"php_sapi_name() === ""cli""",,STATIC_DISPATCH,,90,<operator>.identical,<operator>.identical,,,1,,,ANY
30064771390,CALL,1,,php_sapi_name(),,STATIC_DISPATCH,php_sapi_name,90,php_sapi_name,php_sapi_name,,,1,,<unresolvedSignature>(0),ANY
30064771391,CALL,2,,"isset($_GET[""op""])",,STATIC_DISPATCH,isset,90,isset,isset,,,2,,,bool
30064771392,CALL,1,,"$_GET[""op""]",,STATIC_DISPATCH,,90,<operator>.indexAccess,<operator>.indexAccess,,,1,,,ANY
30064771393,CALL,-1,,"echo handleRequest() . ""<br>\\n""",,STATIC_DISPATCH,echo,91,echo,echo,,,1,,,ANY
30064771394,CALL,1,,"handleRequest() . ""<br>\\n""",,STATIC_DISPATCH,,91,<operator>.concat,<operator>.concat,,,1,,,ANY
30064771395,CALL,1,,handleRequest(),,STATIC_DISPATCH,handleRequest,91,handleRequest,handleRequest,,,1,,<unresolvedSignature>(0),ANY
